<script setup lang="ts" name="DataSourceConfig">
import {ref, reactive, computed, onMounted} from 'vue'
import {ElMessage} from 'element-plus'

import {
	DataSourceConfigItem,
	DATA_SOURCE_CONFIG_MAP,
	getConfigItemName,
} from '@/define/dataSourceConfig'
import {
	getDataSourceConfigs,
	createDataSourceConfig,
	updateDataSourceConfig,
	deleteDataSourceConfig,
} from '@/api/DataSourceConfigApi'
import PreviewDialog from '@/components/common/PreviewDialog.vue'
import {IndexedDBManager} from '@/utils/indexedDB'

// 搜索表单
const searchFormProp = ref([{label: '', prop: 'name', type: 'text'}])
const searchForm = ref({name: ''})

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref<DataSourceConfigItem | null>(null)

// 操作按钮
const buttons = [
	{label: '预览', type: 'info' as const, code: 'preview'},
	{label: '编辑', type: 'primary' as const, code: 'edit'},
	{label: '删除', type: 'danger' as const, code: 'delete', popconfirm: '确认删除吗?'},
]

// 表头配置
const columns = [
	{prop: 'index', label: '序号', width: 80},
	{prop: 'nameZh', label: '名称', minWidth: 150},
	{prop: 'language', label: '语言', width: 120}, // 改为单一语言字段
	{prop: 'creator', label: '创建人', width: 120},
	{prop: 'createTime', label: '创建时间', width: 150},
]

// 分页
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

// 列表请求参数
const reqParams = reactive({
	name: '',
	skipCount: 0,
	maxResultCount: 10,
})

// 表格数据类型（扩展了序号和格式化的创建时间）
interface TableDataItem extends Omit<DataSourceConfigItem, 'createTime'> {
	index: number
	createTime: string
}

// 表格数据
const tableData = ref<TableDataItem[]>([])

// 弹窗表单数据类型
interface DialogFormData {
	configKey?: string
	language?: string
	creator?: string
}

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<DialogFormData>({})

// 预览相关
const showPreview = ref(false)
const previewConfigId = ref('')
const previewConfigKey = ref('')
const previewLanguage = ref('中文') // 新增预览语言变量

// 多语言配置相关
const showMultiLangConfigDialog = ref(false)
const showMultiLangDefineDialog = ref(false)
const multiLangConfigForm = ref({
	sourceLanguage: '中文',
	targetLanguages: ['英文'],
	autoTranslate: true,
	translationEngine: 'google',
	qualityLevel: '高级',
	customTerms: '',
	excludeFields: []
})
const multiLangDefineForm = ref({
	languageCode: 'zh-CN',
	languageName: '中文',
	displayName: '简体中文',
	direction: 'ltr',
	dateFormat: 'YYYY-MM-DD',
	timeFormat: 'HH:mm:ss',
	numberFormat: '1,234.56',
	currencySymbol: '¥',
	isDefault: true
})

// IndexedDB 管理器
const dbManager = new IndexedDBManager('MultiLanguageConfigDB', 1)

// 弹窗表单属性
const dialogFormProps = computed(() => [
	{
		label: '配置名称',
		prop: 'configKey',
		type: 'select',
		options: Object.entries(DATA_SOURCE_CONFIG_MAP).map(([key, config]) => ({
			label: config.nameZh, // 固定显示中文名称
			value: key,
		})),
	},
	{
		label: '支持语言',
		prop: 'language', // 改为单一字段
		type: 'radio', // 改为单选按钮
		options: [
			{label: '中文', value: '中文'},
			{label: '英文', value: '英文'},
		],
	},
	{label: '创建人', prop: 'creator', type: 'text'},
])

// 表单验证规则
const dialogFormRules = {
	configKey: [{required: true, message: '请选择配置名称', trigger: 'change'}],
	language: [{required: true, message: '请选择支持语言', trigger: 'change'}], // 改为单一字段
	creator: [{required: true, message: '请输入创建人', trigger: 'blur'}],
}

// 多语言配置表单属性
const multiLangConfigFormProps = computed(() => [
	{
		label: '源语言',
		prop: 'sourceLanguage',
		type: 'select',
		options: [
			{label: '中文', value: '中文'},
			{label: '英文', value: '英文'},
		],
	},
	{
		label: '目标语言',
		prop: 'targetLanguages',
		type: 'checkbox',
		options: [
			{label: '英文', value: '英文'},
			{label: '日文', value: '日文'},
			{label: '韩文', value: '韩文'},
			{label: '法文', value: '法文'},
			{label: '德文', value: '德文'},
		],
	},
	{
		label: '自动翻译',
		prop: 'autoTranslate',
		type: 'switch',
	},
	{
		label: '翻译引擎',
		prop: 'translationEngine',
		type: 'select',
		options: [
			{label: 'Google翻译', value: 'google'},
			{label: '百度翻译', value: 'baidu'},
			{label: '有道翻译', value: 'youdao'},
		],
	},
	{
		label: '质量等级',
		prop: 'qualityLevel',
		type: 'radio',
		options: [
			{label: '高级', value: '高级'},
			{label: '中级', value: '中级'},
			{label: '低级', value: '低级'},
		],
	},
	{
		label: '自定义术语',
		prop: 'customTerms',
		type: 'textarea',
	},
])

// 多语言定义表单属性
const multiLangDefineFormProps = computed(() => [
	{
		label: '语言代码',
		prop: 'languageCode',
		type: 'text',
	},
	{
		label: '语言名称',
		prop: 'languageName',
		type: 'text',
	},
	{
		label: '显示名称',
		prop: 'displayName',
		type: 'text',
	},
	{
		label: '文字方向',
		prop: 'direction',
		type: 'radio',
		options: [
			{label: '从左到右', value: 'ltr'},
			{label: '从右到左', value: 'rtl'},
		],
	},
	{
		label: '日期格式',
		prop: 'dateFormat',
		type: 'text',
	},
	{
		label: '时间格式',
		prop: 'timeFormat',
		type: 'text',
	},
	{
		label: '数字格式',
		prop: 'numberFormat',
		type: 'text',
	},
	{
		label: '货币符号',
		prop: 'currencySymbol',
		type: 'text',
	},
	{
		label: '默认语言',
		prop: 'isDefault',
		type: 'switch',
	},
])

// 初始化
onMounted(async () => {
	loadTableData()
	await initMultiLangDB()
	await loadMultiLangData()
})

// 初始化多语言数据库
const initMultiLangDB = async () => {
	try {
		await dbManager.init()
	} catch (error) {
		console.error('初始化多语言数据库失败:', error)
	}
}

// 加载多语言数据
const loadMultiLangData = async () => {
	try {
		const configData = await dbManager.getSetting('multiLangConfig')
		const defineData = await dbManager.getSetting('multiLangDefine')

		if (configData) {
			multiLangConfigForm.value = { ...multiLangConfigForm.value, ...configData }
		}
		if (defineData) {
			multiLangDefineForm.value = { ...multiLangDefineForm.value, ...defineData }
		}
	} catch (error) {
		console.error('加载多语言数据失败:', error)
	}
}

// 打开多语言配置对话框
const openMultiLangConfigDialog = () => {
	showMultiLangConfigDialog.value = true
}

// 打开多语言定义对话框
const openMultiLangDefineDialog = () => {
	showMultiLangDefineDialog.value = true
}

// 准确性校验
const validateAccuracy = () => {
	ElMessage.success('校验成功')
}

// 保存多语言配置
const saveMultiLangConfig = async () => {
	try {
		await dbManager.saveSetting('multiLangConfig', multiLangConfigForm.value)
		ElMessage.success('多语言配置保存成功')
		showMultiLangConfigDialog.value = false
	} catch (error) {
		console.error('保存多语言配置失败:', error)
		ElMessage.error('保存失败')
	}
}

// 保存多语言定义
const saveMultiLangDefine = async () => {
	try {
		await dbManager.saveSetting('multiLangDefine', multiLangDefineForm.value)
		ElMessage.success('多语言定义保存成功')
		showMultiLangDefineDialog.value = false
	} catch (error) {
		console.error('保存多语言定义失败:', error)
		ElMessage.error('保存失败')
	}
}

// 加载表格数据
const loadTableData = async () => {
	loading.value = true
	try {
		const response = await getDataSourceConfigs(reqParams)
		tableData.value = response.items.map((item, index) => ({
			...item,
			index: reqParams.skipCount + index + 1,
			createTime: new Date(item.createTime).toLocaleDateString(),
		}))
		pagination.total = response.totalCount
	} catch (error) {
		console.error('加载数据失败:', error)
		ElMessage.error('加载数据失败')
	} finally {
		loading.value = false
	}
}

// 搜索
const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.maxResultCount = pagination.size
	reqParams.name = searchForm.value.name
	loadTableData()
}

// 表格操作点击事件
const onTableClickButton = ({row, btn}: any) => {
	if (btn.code === 'preview') {
		previewConfigId.value = row.id
		previewConfigKey.value = row.configKey || row.id
		previewLanguage.value = row.language || '中文' // 设置预览语言
		showPreview.value = true
	} else if (btn.code === 'edit') {
		currentRow.value = row
		dialogForm.value = {
			configKey: row.configKey || row.id,
			language: row.language, // 改为单一语言字段
			creator: row.creator,
		}
		showDialogForm.value = true
	} else if (btn.code === 'delete') {
		handleDelete(row.id)
	}
}

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await deleteDataSourceConfig(id)
		ElMessage.success('删除成功')
		loadTableData()
	} catch (error) {
		console.error('删除失败:', error)
		ElMessage.error('删除失败')
	}
}

// 新增
const onClickAdd = () => {
	currentRow.value = null
	dialogForm.value = {
		language: '中文', // 改为单一字段，默认选中中文
	}
	showDialogForm.value = true
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
	loadTableData()
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

// 检查配置是否已存在
const checkConfigExists = (configKey: string, language: string): boolean => {
	return tableData.value.some(item => {
		// 编辑时排除当前行
		if (currentRow.value && item.id === currentRow.value.id) {
			return false
		}
		return (item.configKey || item.id) === configKey && item.language === language
	})
}

// 弹框表单提交
const onDialogConfirm = () => {
	dialogFormRef.value.validate(async (valid: boolean) => {
		if (valid) {
			const configKey = dialogForm.value.configKey!
			const language = dialogForm.value.language!

			// 检查配置是否已存在
			if (checkConfigExists(configKey, language)) {
				ElMessage.warning('该配置名称和语言的组合已存在，请选择其他配置或语言')
				return
			}

			loading.value = true
			try {
				const configInfo = DATA_SOURCE_CONFIG_MAP[configKey]

				const formData = {
					configKey: configKey,
					nameZh: configInfo.nameZh,
					nameEn: configInfo.nameEn,
					category: configInfo.category,
					language: language, // 改为单一语言字段
					creator: dialogForm.value.creator!,
				}

				if (currentRow.value) {
					await updateDataSourceConfig(currentRow.value.id, formData)
					ElMessage.success('编辑成功')
				} else {
					await createDataSourceConfig(formData)
					ElMessage.success('新增成功')
				}

				showDialogForm.value = false
				loadTableData()
			} catch (error) {
				console.error('保存失败:', error)
				ElMessage.error('保存失败')
			} finally {
				loading.value = false
			}
		}
	})
}
</script>

<template>
	<div class="data-source-config">
		<Block
			title="多语言配置"
			:enable-fixed-height="true"
			@height-changed="onBlockHeightChanged"
		>
			<template #topRight>
				<el-button size="small" type="info" @click="openMultiLangConfigDialog">多语言配置</el-button>
				<el-button size="small" type="warning" @click="openMultiLangDefineDialog" style="margin-left: 8px;">多语言定义</el-button>
				<el-button size="small" type="primary" @click="onClickAdd" style="margin-left: 8px;">新增</el-button>
			</template>

			<template #expand>
				<!-- 搜索 -->
				<div class="search">
					<Form
						:props="searchFormProp"
						v-model="searchForm"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					/>
				</div>
			</template>

			<!-- 列表 -->
			<el-table
				ref="tableRef"
				:data="tableData"
				:height="tableHeight"
				v-loading="loading"
				stripe
				border
			>
				<el-table-column
					v-for="column in columns"
					:key="column.prop"
					:prop="column.prop"
					:label="column.label"
					:width="column.width"
					:min-width="column.minWidth"
				/>
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="{row}">
						<el-button
							v-for="btn in buttons"
							:key="btn.code"
							:type="btn.type"
							size="small"
							@click="onTableClickButton({row, btn})"
						>
							{{ btn.label }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			<!-- 分页 -->
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			/>
		</Block>

		<!-- 新增/编辑弹窗 -->
		<Dialog
			v-model="showDialogForm"
			:title="currentRow ? '编辑配置' : '新增配置'"
			:destroy-on-close="true"
			:loading="loading"
			loading-text="保存中"
			@closed=";(currentRow = null), (dialogForm = {})"
			@click-confirm="onDialogConfirm"
		>
			<Form
				ref="dialogFormRef"
				v-model="dialogForm"
				:props="dialogFormProps"
				:rules="dialogFormRules"
				:enable-button="false"
			/>
		</Dialog>

		<!-- 预览弹窗 -->
		<PreviewDialog
			v-model:visible="showPreview"
			:config-id="previewConfigId"
			:config-key="previewConfigKey"
			:language="previewLanguage"
			@close="showPreview = false"
		/>

		<!-- 多语言配置对话框 -->
		<Dialog
			v-model="showMultiLangConfigDialog"
			title="多语言配置"
			:destroy-on-close="true"
			width="600px"
			@click-confirm="saveMultiLangConfig"
		>
			<Form
				v-model="multiLangConfigForm"
				:props="multiLangConfigFormProps"
				:enable-button="false"
			/>
			<template #footer>
				<el-button @click="showMultiLangConfigDialog = false">取消</el-button>
				<el-button type="info" @click="validateAccuracy">准确性校验</el-button>
				<el-button type="primary" @click="saveMultiLangConfig">确定</el-button>
			</template>
		</Dialog>

		<!-- 多语言定义对话框 -->
		<Dialog
			v-model="showMultiLangDefineDialog"
			title="多语言定义"
			:destroy-on-close="true"
			width="600px"
			@click-confirm="saveMultiLangDefine"
		>
			<Form
				v-model="multiLangDefineForm"
				:props="multiLangDefineFormProps"
				:enable-button="false"
			/>
		</Dialog>
	</div>
</template>

<route>
{
  meta: {
    title: '多语言配置'
  }
}
</route>

<style scoped lang="scss">
.data-source-config {
	.search {
		margin-bottom: 16px;
	}
}
</style>
