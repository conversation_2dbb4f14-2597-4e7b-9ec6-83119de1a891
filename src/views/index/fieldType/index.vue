<script setup lang="ts" name="fieldType">
import { ref, onMounted, nextTick, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Upload, Download, UploadFilled } from '@element-plus/icons-vue'
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { router } from '@/router'

// 字段类型数据接口
interface FieldType {
  id: string
  name: string
  type: string
  minLength: number
  maxLength: number
  status: boolean
  regex?: string
  createTime: string
  updateTime: string
  group: string // 所属分组
  typeName: string // 字段信息名称
}

// 本地缓存key
const CACHE_KEY = 'fieldTypeList'
const UPDATE_LOG_CACHE_KEY = 'fieldTypeUpdateLog'
const FIELD_INFO_CACHE_KEY = 'fieldInfoList'
const FIELD_GROUP_CACHE_KEY = 'fieldGroupList'
const DATA_ENCRYPTION_CACHE_KEY = 'dataEncryptionList'

// 更新日志数据接口
interface UpdateLog {
  id: string
  content: string
  version: string
  updateTime: string
  operator: string
}

// 字段信息数据接口
interface FieldInfo {
  id: string
  name: string
  updateTime: string
  operator: string
}

// 字段分组数据接口
interface FieldGroup {
  id: string
  name: string
  updateTime: string
  operator: string
}

// 数据加密接口
interface DataEncryption {
  id: string
  encryptType: string
  encryptKey: string
  description: string
}

// 搜索表单
const searchFormProp = ref([
  {title: '请输入字段名称', field: 'name', type: 'input'},
  {title: '请选择字段类型', field: 'type', type: 'select', data: [
    {label: '字符型', value: '字符型'},
    {label: '数值型', value: '数值型'},
    {label: '浮点型', value: '浮点型'},
    {label: '日期型', value: '日期型'},
    {label: 'image类型', value: 'image类型'},
    {label: '布尔型', value: '布尔型'}
  ]},
  {title: '请输入最小长度', field: 'minLength', type: 'decimal'},
  {title: '请输入最大长度', field: 'maxLength', type: 'decimal'}
])
const searchForm = ref({name: '', type: '', minLength: undefined as number | undefined, maxLength: undefined as number | undefined})

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(850) // 表格高度
const currentRow = ref<FieldType | null>(null)
const selectedRows = ref<FieldType[]>([])
const searchFormRef = ref()

// 操作按钮
const buttons = ref([
  {title: '详情', code: 'detail', type: 'primary'},
  {title: '编辑', code: 'edit', type: 'primary'},
  {title: '删除', code: 'delete', type: 'danger'},
  {title: '数据加密', code: 'encrypt', type: 'warning'},
])

// 表头配置
const columns = ref([
  {title: '字段类型名称', field: 'name', minWidth: '150px'},
  {title: '所属分组', field: 'group', minWidth: '150px'},
  {title: '字段信息名称', field: 'typeName', minWidth: '150px'},
  {title: '字段类型', field: 'type', width: '120px'},
  {title: '最小长度', field: 'minLength', width: '100px', sortable: 'custom'},
  {title: '最大长度', field: 'maxLength', width: '100px', sortable: 'custom'},
  {title: '状态', field: 'status', width: '100px'},
])

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})

// 列表数据
const tableData = ref<FieldType[]>([])
const filteredData = ref<FieldType[]>([])

// 弹窗状态
const showDialogForm = ref(false)
const showDetailDialog = ref(false)
const showAutoFillDialog = ref(false)
const showUserPermissionDialog = ref(false)
const showDataCompareDialog = ref(false)
const showFieldInfoDialog = ref(false)
const showFieldGroupDialog = ref(false)
const showDataEncryptionDialog = ref(false)
const showFieldInfoFormDialog = ref(false)
const showFieldGroupFormDialog = ref(false)
const dialogFormRef = ref()
const fieldInfoFormRef = ref()
const fieldGroupFormRef = ref()
const dataEncryptionFormRef = ref()

// 弹窗表单数据
const dialogForm = ref<Partial<FieldType>>({})
const fieldInfoForm = ref<Partial<FieldInfo>>({})
const fieldGroupForm = ref<Partial<FieldGroup>>({})
const dataEncryptionForm = ref<Partial<DataEncryption>>({})

// 新功能的数据列表
const fieldInfoList = ref<FieldInfo[]>([])
const fieldGroupList = ref<FieldGroup[]>([])
const dataEncryptionList = ref<DataEncryption[]>([])

// 新功能的弹窗状态
const fieldInfoDialogMode = ref<'add' | 'edit' | 'detail'>('add')
const fieldGroupDialogMode = ref<'add' | 'edit' | 'detail'>('add')
const dataEncryptionDialogMode = ref<'add' | 'edit' | 'detail'>('add')

// 新功能的选中行
const selectedFieldInfoRows = ref<FieldInfo[]>([])
const selectedFieldGroupRows = ref<FieldGroup[]>([])

// 新功能的分页
const fieldInfoPagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})

const fieldGroupPagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})

// 字段类型选项（响应式）
const fieldTypeOptions = ref([])

// 获取字段类型选项（仅从自动填充规则中获取）
const getFieldTypeOptions = () => {
  const autoFillRules = localStorage.getItem(AUTO_FILL_CACHE_KEY)
  if (!autoFillRules) {
    return [] // 如果没有设置自动填充规则，返回空数组
  }

  const rules = JSON.parse(autoFillRules)
  const savedDataTypes = Array.isArray(rules.dataType) ? rules.dataType : (rules.dataType ? [rules.dataType] : [])

  return savedDataTypes.map(type => ({ label: type, value: type }))
}

// 更新字段类型选项
const updateFieldTypeOptions = () => {
  fieldTypeOptions.value = getFieldTypeOptions()
}

// 弹窗表单属性配置
const dialogFormProps = computed(() => [
  {label: '字段信息选择', prop: 'fieldInfoId', type: 'select', placeholder: '请选择字段信息', options: fieldInfoList.value.map(item => ({label: item.name, value: item.id}))},
  {label: '所属分组', prop: 'fieldGroupId', type: 'select', placeholder: '请选择所属分组', options: fieldGroupList.value.map(item => ({label: item.name, value: item.id}))},
  {label: '类型名称', prop: 'name', type: 'text', placeholder: '请输入字段类型名称'},
  {label: '字段类型', prop: 'type', type: 'select', placeholder: '请选择字段类型', options: fieldTypeOptions.value},
  {label: '最小长度', prop: 'minLength', type: 'text', inputType: 'number', placeholder: '请输入最小长度'},
  {label: '最大长度', prop: 'maxLength', type: 'text', inputType: 'number', placeholder: '请输入最大长度'},
  {label: '正则表达式', prop: 'regex', type: 'textarea', placeholder: '请输入正则表达式（可选）'}
])
// 详情表单属性配置（只读）
const detailFormProps = ref([
  {label: '类型名称', prop: 'name', type: 'text', disabled: true},
  {label: '字段类型', prop: 'type', type: 'select', disabled: true, options: [
    {label: '字符型', value: '字符型'},
    {label: '数值型', value: '数值型'},
    {label: 'image类型', value: 'image类型'},
    {label: '日期型', value: '日期型'}
  ]},
  {label: '最小长度', prop: 'minLength', type: 'text', inputType: 'number', disabled: true},
  {label: '最大长度', prop: 'maxLength', type: 'text', inputType: 'number', disabled: true},
  {label: '状态', prop: 'statusText', type: 'text', disabled: true},
  {label: '创建时间', prop: 'createTime', type: 'text', disabled: true},
  {label: '正则表达式', prop: 'regex', type: 'textarea', disabled: true},
  {label: '更新时间', prop: 'updateTime', type: 'text', disabled: true}
])




// 表单验证规则
const dialogFormRules = {
  fieldInfoId: [{required: true, message: '请选择字段信息', trigger: 'change'}],
  fieldGroupId: [{required: true, message: '请选择所属分组', trigger: 'change'}],
  name: [{required: true, message: '请输入字段类型名称', trigger: 'blur'}],
  type: [{required: true, message: '请选择字段类型', trigger: 'change'}],
  minLength: [
    {required: true, message: '请输入最小长度', trigger: 'blur'},
    {pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur'}
  ],
  maxLength: [
    {required: true, message: '请输入最大长度', trigger: 'blur'},
    {pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur'}
  ],
}

// 字段信息表单配置
const fieldInfoFormProps = ref([
  {label: '字段信息', prop: 'name', type: 'text', placeholder: '请输入字段信息'},
  {label: '更新时间', prop: 'updateTime', type: 'text', placeholder: '请输入更新时间'},
  {label: '创建人', prop: 'operator', type: 'text', placeholder: '请输入创建人'}
])

// 字段分组表单配置
const fieldGroupFormProps = ref([
  {label: '常用字段分组名称', prop: 'name', type: 'text', placeholder: '请输入常用字段分组名称'},
  {label: '更新时间', prop: 'updateTime', type: 'text', placeholder: '请输入更新时间'},
  {label: '创建人', prop: 'operator', type: 'text', placeholder: '请输入创建人'}
])

// 数据加密表单配置
const dataEncryptionFormProps = ref([
  {label: '加密类型', prop: 'encryptType', type: 'select', placeholder: '请选择加密类型', options: [
    {label: 'AES', value: 'AES'},
    {label: 'RSA', value: 'RSA'},
    {label: 'MD5', value: 'MD5'}
  ]},
  {label: '加密密钥', prop: 'encryptKey', type: 'text', placeholder: '请输入加密密钥'},
  {label: '描述', prop: 'description', type: 'textarea', placeholder: '请输入描述'}
])

// 表单验证规则
const fieldInfoFormRules = {
  name: [{required: true, message: '请输入字段信息', trigger: 'blur'}],
  updateTime: [{required: true, message: '请输入更新时间', trigger: 'blur'}],
  operator: [{required: true, message: '请输入创建人', trigger: 'blur'}]
}

const fieldGroupFormRules = {
  name: [{required: true, message: '请输入常用字段分组名称', trigger: 'blur'}],
  updateTime: [{required: true, message: '请输入更新时间', trigger: 'blur'}],
  operator: [{required: true, message: '请输入创建人', trigger: 'blur'}]
}

const dataEncryptionFormRules = {
  encryptType: [{required: true, message: '请选择加密类型', trigger: 'change'}],
  encryptKey: [{required: true, message: '请输入加密密钥', trigger: 'blur'}]
}

// 获取随机字段分组
const getRandomFieldGroup = (): string => {
  const groups = ['基础信息分组', '个人信息分组', '业务信息分组', '医疗健康分组', '教育培训分组', '证件证书分组', '交通运输分组', '金融贸易分组', '安全检测分组', '系统管理分组']
  return groups[Math.floor(Math.random() * groups.length)]
}

// 获取随机字段信息名称
const getRandomFieldTypeName = (): string => {
  const typeNames = ['基础字段类型', '复杂字段', '特殊字段', '文本输入字段', '数值计算字段', '日期时间字段', '图片上传字段', '下拉选择字段', '多选框字段', '单选按钮字段']
  return typeNames[Math.floor(Math.random() * typeNames.length)]
}

// 初始化mock数据
const initMockData = (): FieldType[] => {
  return [
    {id: '1', name: '民间借贷活动情况信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-03-21', updateTime: '2024-09-14', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '2', name: '业务线索来源数据展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-07-10', updateTime: '2024-02-04', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '3', name: '个人立案信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-04-19', updateTime: '2024-12-28', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '4', name: '案件处理结果信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-06-15', updateTime: '2024-08-22', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '5', name: '新生儿出生证明信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-01-27', updateTime: '2024-11-03', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '6', name: '夫妻双方结婚证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-05-12', updateTime: '2024-03-09', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '7', name: '独生子女证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-02-18', updateTime: '2024-07-30', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '8', name: '个人健康证申请信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-09-08', updateTime: '2024-10-19', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '9', name: '涉外婚姻证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-11-22', updateTime: '2024-06-04', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '10', name: '居民死亡证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-08-16', updateTime: '2024-04-27', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '11', name: '残疾人证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-10-02', updateTime: '2024-12-01', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '12', name: '老人证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-12-11', updateTime: '2024-05-17', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '13', name: '毕业证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-03-30', updateTime: '2024-08-08', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '14', name: '学位证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-07-24', updateTime: '2024-09-27', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '15', name: '教师资格证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-01-14', updateTime: '2024-06-29', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '16', name: '个人行驶证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-02-22', updateTime: '2024-07-18', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '17', name: '车辆营运证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-05-03', updateTime: '2024-11-23', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '18', name: '企业税务登记证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-04-08', updateTime: '2024-10-07', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '19', name: '驾驶员驾驶证信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-06-21', updateTime: '2024-03-31', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '20', name: '国际区号信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-08-13', updateTime: '2024-01-19', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '21', name: '全国邮政编码信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-03-08', updateTime: '2024-09-06', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '22', name: '商品条形码的产品描述信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-07-16', updateTime: '2024-11-12', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '23', name: '地理位置的详细地址信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-09-25', updateTime: '2024-04-10', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '24', name: '颜色代码的名称关联信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-10-15', updateTime: '2024-12-21', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '25', name: '图片描述信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-01-09', updateTime: '2024-07-03', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '26', name: '音频路径及名称信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-11-29', updateTime: '2024-06-11', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '27', name: '视颉路径及标题信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-02-26', updateTime: '2024-08-25', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '28', name: '书本出版日期信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-04-13', updateTime: '2024-10-30', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '29', name: '个人生存状态信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-06-07', updateTime: '2024-12-15', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '30', name: '个人生活状态信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-08-02', updateTime: '2024-02-14', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '31', name: '个人工作状态信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-03-17', updateTime: '2024-09-18', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '32', name: '个人婚姻情况信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-10-26', updateTime: '2024-05-29', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '33', name: '老年人信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-05-21', updateTime: '2024-11-16', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '34', name: '规上企业信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-07-07', updateTime: '2024-03-26', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '35', name: '规下企业信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-12-04', updateTime: '2024-06-25', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '36', name: '高新科技公司信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-09-02', updateTime: '2024-08-29', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '37', name: '高校名称信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-02-09', updateTime: '2024-04-22', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '38', name: '基层网格员信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-11-08', updateTime: '2024-12-08', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '39', name: '基层网格员职责分配信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-03-04', updateTime: '2024-07-27', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '40', name: '车辆处罚信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-05-27', updateTime: '2024-09-10', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '41', name: '个人教育背景信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-01-31', updateTime: '2024-11-26', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '42', name: '个人工作年限信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-10-11', updateTime: '2024-05-05', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '43', name: '公司入员组成信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-06-11', updateTime: '2024-02-25', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '44', name: '公司年收入信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-08-20', updateTime: '2024-03-13', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '45', name: '社会组织基本描述信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-04-05', updateTime: '2024-07-21', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '46', name: '社会组织工作情況信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-09-12', updateTime: '2024-10-24', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '47', name: '社会组织人员构成信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-12-17', updateTime: '2024-06-19', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '48', name: '社会组织奖惩情况信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-02-13', updateTime: '2024-08-05', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '49', name: '社会组织司法纠纷信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-07-13', updateTime: '2024-12-25', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '50', name: '个人健康状态信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-04-25', updateTime: '2024-01-23', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '51', name: '少数民族分布数据展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-06-18', updateTime: '2024-09-23', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '52', name: '少数民族人员数据展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-10-05', updateTime: '2024-05-14', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '53', name: '宗教信仰信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-12-29', updateTime: '2024-07-15', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '54', name: '企业登记信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-03-25', updateTime: '2024-11-07', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '55', name: '企业存续状态信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-08-27', updateTime: '2024-02-19', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '56', name: '企业场所信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-01-05', updateTime: '2024-10-13', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '57', name: '企业股东信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-11-18', updateTime: '2024-06-01', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '58', name: '企业备案信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-04-16', updateTime: '2024-12-12', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '59', name: '企业资质信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-07-19', updateTime: '2024-03-23', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '60', name: '企业法人信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-09-05', updateTime: '2024-05-26', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '61', name: '企业许可资质信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-12-22', updateTime: '2024-08-18', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '62', name: '企业纳税缴税信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-02-06', updateTime: '2024-11-20', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '63', name: '企业生产经营信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-05-09', updateTime: '2024-04-02', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '64', name: '企业行政执法信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-10-21', updateTime: '2024-07-12', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '65', name: '企业司法信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-01-17', updateTime: '2024-09-01', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '66', name: '企业信用评价信息展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-06-29', updateTime: '2024-11-29', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '67', name: '人口民族分布展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-03-11', updateTime: '2024-05-01', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '68', name: '人口地域分布展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-12-02', updateTime: '2024-10-16', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()},
    {id: '69', name: '人口性别分布展示', type: '字符型', minLength: 10, maxLength: 50, status: true, regex: '', createTime: '2024-04-29', updateTime: '2024-08-15', group: getRandomFieldGroup(), typeName: getRandomFieldTypeName()}]
}

// 本地缓存操作
const loadFromCache = (): FieldType[] => {
  const cached = localStorage.getItem(CACHE_KEY)
  if (cached) {
    return JSON.parse(cached)
  }
  const mockData = initMockData()
  saveToCache(mockData)
  return mockData
}

const saveToCache = (data: FieldType[]) => {
  localStorage.setItem(CACHE_KEY, JSON.stringify(data))
}

// 更新日志缓存操作
const loadUpdateLogFromCache = (): UpdateLog[] => {
  const cached = localStorage.getItem(UPDATE_LOG_CACHE_KEY)
  if (cached) {
    return JSON.parse(cached)
  }
  // 返回空数组，不使用模拟数据
  return []
}

const saveUpdateLogToCache = (data: UpdateLog[]) => {
  localStorage.setItem(UPDATE_LOG_CACHE_KEY, JSON.stringify(data))
}

// 字段信息缓存操作
const loadFieldInfoFromCache = (): FieldInfo[] => {
  const cached = localStorage.getItem(FIELD_INFO_CACHE_KEY)
  if (cached) {
    return JSON.parse(cached)
  }
  return []
}

const saveFieldInfoToCache = (data: FieldInfo[]) => {
  localStorage.setItem(FIELD_INFO_CACHE_KEY, JSON.stringify(data))
}

// 字段分组缓存操作
const loadFieldGroupFromCache = (): FieldGroup[] => {
  const cached = localStorage.getItem(FIELD_GROUP_CACHE_KEY)
  if (cached) {
    return JSON.parse(cached)
  }
  return []
}

const saveFieldGroupToCache = (data: FieldGroup[]) => {
  localStorage.setItem(FIELD_GROUP_CACHE_KEY, JSON.stringify(data))
}

// 数据加密缓存操作
const loadDataEncryptionFromCache = (): DataEncryption[] => {
  const cached = localStorage.getItem(DATA_ENCRYPTION_CACHE_KEY)
  if (cached) {
    return JSON.parse(cached)
  }
  return []
}

const saveDataEncryptionToCache = (data: DataEncryption[]) => {
  localStorage.setItem(DATA_ENCRYPTION_CACHE_KEY, JSON.stringify(data))
}

// 重置缓存数据为最新的模拟数据
const resetCacheData = () => {
  const mockData = initMockData()
  saveToCache(mockData)
  tableData.value = mockData
  applyFilters()
  ElMessage.success('数据已重置为最新数据')
}

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9)
}

// 应用筛选和分页
const applyFilters = () => {
  let filtered = [...tableData.value]

  // 应用搜索筛选
  if (searchForm.value.name) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }
  if (searchForm.value.type) {
    filtered = filtered.filter(item => item.type === searchForm.value.type)
  }
  if (searchForm.value.minLength !== undefined && searchForm.value.minLength !== null) {
    filtered = filtered.filter(item => item.minLength >= searchForm.value.minLength!)
  }
  if (searchForm.value.maxLength !== undefined && searchForm.value.maxLength !== null) {
    filtered = filtered.filter(item => item.maxLength <= searchForm.value.maxLength!)
  }

  filteredData.value = filtered
  pagination.total = filtered.length

  // 重置到第一页
  if (pagination.page > Math.ceil(pagination.total / pagination.size)) {
    pagination.page = 1
  }
}

// 获取当前页数据
const getCurrentPageData = () => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
}

// 查询
const onSearch = () => {
  pagination.page = 1
  applyFilters()
}

// 重置
const onReset = () => {
  searchForm.value = {name: '', type: '', minLength: undefined, maxLength: undefined}
  pagination.page = 1
  applyFilters()
}

// 表格操作点击事件
const onTableClickButton = ({btn, scope}: any) => {
  // 尝试不同的方式获取行数据
  const row = scope?.row || scope

  if (btn.code === 'detail') {
    // 为详情弹窗添加状态文本
    currentRow.value = {
      ...row,
      statusText: row.status ? '启用' : '停用'
    }
    showDetailDialog.value = true
  } else if (btn.code === 'edit') {
    currentRow.value = row
    Object.assign(dialogForm.value, row)
    showDialogForm.value = true
  } else if (btn.code === 'delete') {
    deleteRecord(row.id)
  } else if (btn.code === 'encrypt') {
    onDataEncryption(row)
  }
}

// 更新日志相关数据
const updateLogDialogVisible = ref(false)
const addUpdateLogDialogVisible = ref(false)
const updateLogLoading = ref(false)
const updateLogList = ref<UpdateLog[]>([])
const updateLogForm = ref<UpdateLog>({
  id: '',
  content: '',
  version: '',
  updateTime: '',
  operator: ''
})
const updateLogFormRef = ref()

// 批量导入相关数据
const batchImportDialogVisible = ref(false)
const importFileList = ref<any[]>([])
const importLoading = ref(false)
const selectedFileName = ref('')

// 字段信息批量导入相关数据
const fieldInfoImportDialogVisible = ref(false)
const fieldInfoImportFileList = ref<any[]>([])
const fieldInfoImportLoading = ref(false)
const fieldInfoSelectedFileName = ref('')

// 更新日志表单配置
const updateLogFormProps = ref([
  {title: '更新内容', field: 'content', type: 'input', required: true, placeholder: '请输入'},
  {title: '更新时间', field: 'updateTime', type: 'input', required: true, placeholder: '请输入'},
  {title: '创建人', field: 'operator', type: 'input', required: true, placeholder: '请输入'},
  {title: '更新版本', field: 'version', type: 'input', required: true, placeholder: '请输入'}
])

const updateLogFormRules = ref({
  content: [{required: true, message: '请输入更新内容', trigger: 'blur'}],
  updateTime: [{required: true, message: '请选择更新时间', trigger: 'change'}],
  operator: [{required: true, message: '请输入创建人', trigger: 'blur'}],
  version: [{required: true, message: '请输入更新版本', trigger: 'blur'}]
})

// 新增
const onClickAdd = () => {
  currentRow.value = null
  Object.assign(dialogForm.value, {})
  showDialogForm.value = true
}



// 批量删除
const onBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  ElMessageBox.confirm(
    `确认删除选中的 ${selectedRows.value.length} 条记录吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const idsToDelete = selectedRows.value.map(row => row.id)
    tableData.value = tableData.value.filter(item => !idsToDelete.includes(item.id))
    saveToCache(tableData.value)
    selectedRows.value = []
    applyFilters()
    ElMessage.success('批量删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 删除单条记录
const deleteRecord = (id: string) => {
  tableData.value = tableData.value.filter(item => item.id !== id)
  saveToCache(tableData.value)
  applyFilters()
  ElMessage.success('删除成功')
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  // 为搜索区域预留更多空间，因为有4个搜索字段
  tableHeight.value = height - 120
}

// 表格选择变化
const onSelectionChange = (selection: FieldType[]) => {
  console.log('onSelectionChange triggered:', selection)
  selectedRows.value = selection
  console.log('selectedRows.value updated:', selectedRows.value)
}

// 计算属性：选中行数量
const selectedRowsCount = computed(() => {
  return selectedRows.value?.length || 0
})

// 弹框表单提交
const onDialogConfirm = () => {
  if (!dialogFormRef.value) return

  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const formData = dialogForm.value

      // 验证最大长度必须大于等于最小长度
      if (Number(formData.maxLength) < Number(formData.minLength)) {
        ElMessage.error('最大长度不能小于最小长度')
        return
      }

      // 检查名称重复（编辑时排除自身）
      const existingItem = tableData.value.find(item =>
        item.name === formData.name &&
        (!currentRow.value || item.id !== currentRow.value.id)
      )

      if (existingItem) {
        ElMessage.error('字段类型名称已存在')
        return
      }

      loading.value = true

      setTimeout(() => {
        try {
          if (currentRow.value) {
            // 编辑
            const index = tableData.value.findIndex(item => item.id === currentRow.value!.id)
            if (index !== -1) {
              tableData.value[index] = {
                ...tableData.value[index],
                name: formData.name,
                type: formData.type,
                minLength: Number(formData.minLength),
                maxLength: Number(formData.maxLength),
                regex: formData.regex || '',
                updateTime: new Date().toISOString().split('T')[0]
              } as FieldType
            }
            ElMessage.success('编辑成功')
          } else {
            // 新增
            const newItem: FieldType = {
              id: generateId(),
              name: formData.name,
              type: formData.type,
              minLength: Number(formData.minLength),
              maxLength: Number(formData.maxLength),
              status: true,
              regex: formData.regex || '',
              createTime: new Date().toISOString().split('T')[0],
              updateTime: new Date().toISOString().split('T')[0]
            }
            tableData.value.unshift(newItem)
            ElMessage.success('新增成功')
          }

          saveToCache(tableData.value)
          applyFilters()
          showDialogForm.value = false
        } catch (error) {
          ElMessage.error('操作失败')
        } finally {
          loading.value = false
        }
      }, 500) // 模拟网络延迟
    }
  })
}

// 状态切换
const onStatusChange = (row: FieldType) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    tableData.value[index].status = row.status
    tableData.value[index].updateTime = new Date().toISOString().split('T')[0]
    saveToCache(tableData.value)
    ElMessage.success(`已${row.status ? '启用' : '停用'}`)
  }
}

// 排序处理 - BaseTableComp 内置排序，这里不需要额外处理
const onSortChange = (data: any) => {
  // BaseTableComp 会自动处理排序
  console.log('排序变化:', data)
}

// 新增按钮方法
const onBatchExport = async () => {
  // 确定要导出的数据：如果有选中数据则导出选中的，否则导出全部数据
  const exportData = selectedRows.value.length > 0 ? selectedRows.value : filteredData.value
  const exportType = selectedRows.value.length > 0 ? '选中' : '全部'

  if (exportData.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('字段类型数据')

    // 设置表头
    const headers = [
      { header: '序号', key: 'id', width: 10 },
      { header: '字段名称', key: 'name', width: 30 },
      { header: '字段类型', key: 'type', width: 15 },
      { header: '最小长度', key: 'minLength', width: 12 },
      { header: '最大长度', key: 'maxLength', width: 12 },
      { header: '状态', key: 'status', width: 10 },
      { header: '正则表达式', key: 'regex', width: 20 },
      { header: '创建时间', key: 'createTime', width: 15 },
      { header: '更新时间', key: 'updateTime', width: 15 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加数据
    exportData.forEach((row, index) => {
      worksheet.addRow({
        id: index + 1,
        name: row.name,
        type: row.type,
        minLength: row.minLength,
        maxLength: row.maxLength,
        status: row.status ? '启用' : '停用',
        regex: row.regex || '',
        createTime: row.createTime,
        updateTime: row.updateTime
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const fileName = `字段类型数据_${exportType}_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)

    ElMessage.success(`成功导出${exportType} ${exportData.length} 条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const onBatchImport = () => {
  batchImportDialogVisible.value = true
  importFileList.value = []
  selectedFileName.value = ''
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('字段类型模板')

    // 设置表头
    const headers = [
      { header: '字段名称', key: 'name', width: 30 },
      { header: '字段类型', key: 'type', width: 15 },
      { header: '最小长度', key: 'minLength', width: 12 },
      { header: '最大长度', key: 'maxLength', width: 12 },
      { header: '正则表达式', key: 'regex', width: 20 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加示例数据
    worksheet.addRow({
      name: '示例字段名称',
      type: '字符型',
      minLength: 1,
      maxLength: 50,
      regex: ''
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const fileName = `字段类型导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败，请重试')
  }
}

// 处理文件选择（on-change事件）
const handleFileSelect = (file: any, fileList: any[]) => {
  console.log('文件选择事件:', file, fileList)

  // 检查文件类型
  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
    ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
    return
  }

  // 检查文件大小（限制为10MB）
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return
  }

  // 保存文件信息
  importFileList.value = [file]
  selectedFileName.value = file.name
  ElMessage.success('文件选择成功，点击上传按钮开始导入')
}

// 处理文件上传前验证（before-upload事件）
const handleFileChange = (file: any) => {
  console.log('文件上传前验证:', file)
  return false // 阻止自动上传
}

// 处理文件移除
const handleFileRemove = () => {
  importFileList.value = []
  selectedFileName.value = ''
}

// 上传并解析Excel文件
const uploadFile = async () => {
  console.log('开始上传文件:', importFileList.value, selectedFileName.value)

  if (importFileList.value.length === 0 || !selectedFileName.value) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  // 获取文件对象，优先使用raw属性
  const file = importFileList.value[0].raw || importFileList.value[0]
  console.log('获取到的文件对象:', file)

  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
    ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
    return
  }

  importLoading.value = true

  try {
    const buffer = await file.arrayBuffer()
    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.load(buffer)

    const worksheet = workbook.getWorksheet(1)
    if (!worksheet) {
      throw new Error('Excel文件中没有找到工作表')
    }

    const importData: FieldType[] = []
    const errors: string[] = []
    const validTypes = ['字符型', '数值型', 'image类型', '日期型', '日期类型']

    // 从第二行开始读取数据（第一行是表头）
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return // 跳过表头

      const values = row.values as any[]
      const name = values[1]?.toString()?.trim()
      const type = values[2]?.toString()?.trim()
      const minLength = Number(values[3]) || 0
      const maxLength = Number(values[4]) || 0
      const regex = values[5]?.toString()?.trim() || ''

      // 跳过完全空白的行
      if (!name && !type && !minLength && !maxLength) {
        return
      }

      // 验证必填字段
      if (!name) {
        errors.push(`第${rowNumber}行：字段名称不能为空`)
        return
      }
      if (!type) {
        errors.push(`第${rowNumber}行：字段类型不能为空`)
        return
      }

      // 验证字段类型是否有效
      if (!validTypes.includes(type)) {
        errors.push(`第${rowNumber}行：字段类型"${type}"无效，请使用：${validTypes.join('、')}`)
        return
      }

      // 验证长度
      if (minLength < 0) {
        errors.push(`第${rowNumber}行：最小长度不能小于0`)
        return
      }
      if (maxLength < 0) {
        errors.push(`第${rowNumber}行：最大长度不能小于0`)
        return
      }
      if (maxLength < minLength) {
        errors.push(`第${rowNumber}行：最大长度不能小于最小长度`)
        return
      }

      // 检查字段名称是否重复
      const existingItem = tableData.value.find(item => item.name === name)
      if (existingItem) {
        errors.push(`第${rowNumber}行：字段名称"${name}"已存在`)
        return
      }

      // 检查导入数据中是否有重复
      const duplicateInImport = importData.find(item => item.name === name)
      if (duplicateInImport) {
        errors.push(`第${rowNumber}行：字段名称"${name}"在导入数据中重复`)
        return
      }

      const newItem: FieldType = {
        id: generateId(),
        name,
        type,
        minLength,
        maxLength,
        status: true,
        regex,
        createTime: new Date().toISOString().split('T')[0],
        updateTime: new Date().toISOString().split('T')[0]
      }

      importData.push(newItem)
    })

    if (errors.length > 0) {
      // 限制错误信息显示数量，避免过长
      const displayErrors = errors.slice(0, 10)
      const errorMessage = displayErrors.join('\n') + (errors.length > 10 ? `\n...还有${errors.length - 10}个错误` : '')
      ElMessage.error(`导入失败，发现以下错误：\n${errorMessage}`)
      return
    }

    if (importData.length === 0) {
      ElMessage.warning('没有找到有效的数据，请检查Excel文件内容')
      return
    }

    // 添加到表格数据
    tableData.value.push(...importData)
    saveToCache(tableData.value)
    applyFilters()

    ElMessage.success(`成功导入 ${importData.length} 条数据`)
    batchImportDialogVisible.value = false
    importFileList.value = []
    selectedFileName.value = ''

  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error(`文件解析失败：${error.message || '请检查文件格式'}`)
  } finally {
    importLoading.value = false
  }
}

const onUpdateLog = () => {
  updateLogList.value = loadUpdateLogFromCache()
  updateLogDialogVisible.value = true
}

const onFieldTemplate = () => {
  // 跳转到字段信息模板页面
  router.push('/fieldTemplate')
}

const onMoreAction = (command: string) => {
  if (command === 'autoFill') {
    showAutoFillDialog.value = true
  } else if (command === 'fieldUsage') {
    // 跳转到字段信息使用统计页面
    router.push('/fieldStatistics')
  } else if (command === 'userPermission') {
    showUserPermissionDialog.value = true
  } else if (command === 'dataCompare') {
    showDataCompareDialog.value = true
  }
}

// 更新日志相关方法
const onAddUpdateLog = () => {
  resetUpdateLogForm()
  addUpdateLogDialogVisible.value = true
}

const resetUpdateLogForm = () => {
  updateLogForm.value = {
    id: '',
    content: '',
    version: '',
    updateTime: '',
    operator: ''
  }
  // 清除表单验证
  if (updateLogFormRef.value) {
    updateLogFormRef.value.clearValidate()
  }
}

const onSaveUpdateLog = () => {
  if (!updateLogFormRef.value) return

  updateLogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      updateLogLoading.value = true

      setTimeout(() => {
        try {
          const newLog: UpdateLog = {
            ...updateLogForm.value,
            id: (updateLogList.value.length + 1).toString().padStart(2, '0')
          }

          updateLogList.value.unshift(newLog)
          saveUpdateLogToCache(updateLogList.value)

          ElMessage.success('新增更新日志成功')
          addUpdateLogDialogVisible.value = false
          resetUpdateLogForm()
        } catch (error) {
          ElMessage.error('保存失败')
        } finally {
          updateLogLoading.value = false
        }
      }, 1000)
    }
  })
}

// 自动填充数据功能
const autoFillForm = ref({
  sequence: '',
  dataType: []
})

const autoFillFormRef = ref()

// 预设数据类型选项
const dataTypeOptions = [
  { label: '字符型', value: '字符型' },
  { label: '数值型', value: '数值型' },
  { label: '浮点型', value: '浮点型' },
  { label: '日期型', value: '日期型' },
  { label: 'image类型', value: 'image类型' },
  { label: '布尔型', value: '布尔型' }
]

// 自动填充规则缓存key
const AUTO_FILL_CACHE_KEY = 'autoFillRules'

// 加载自动填充规则
const loadAutoFillRules = () => {
  const cached = localStorage.getItem(AUTO_FILL_CACHE_KEY)
  if (cached) {
    const rules = JSON.parse(cached)
    autoFillForm.value = rules
  }
}

// 保存自动填充规则
const saveAutoFillRules = () => {
  localStorage.setItem(AUTO_FILL_CACHE_KEY, JSON.stringify(autoFillForm.value))
}

const onAutoFillConfirm = () => {
  if (!autoFillFormRef.value) return

  autoFillFormRef.value.validate((valid: boolean) => {
    if (valid) {
      // 保存到本地缓存
      saveAutoFillRules()
      // 更新字段类型选项
      updateFieldTypeOptions()
      ElMessage.success('自动填充规则设置成功')
      showAutoFillDialog.value = false
    }
  })
}

// 用户权限功能
const userPermissionForm = ref({
  authorizedUsers: [],
  permissions: []
})

// Mock用户数据
const mockUsers = [
  { label: '张三', value: '张三' },
  { label: '李四', value: '李四' },
  { label: '王五', value: '王五' },
  { label: 'AA', value: 'AA' },
  { label: 'BB', value: 'BB' },
  { label: 'CC', value: 'CC' },
  { label: 'DD', value: 'DD' }
]

const permissionOptions = [
  { label: '查看', value: 'view' },
  { label: '新增', value: 'add' },
  { label: '编辑', value: 'edit' },
  { label: '删除', value: 'delete' }
]

const onUserPermissionConfirm = () => {
  // 保存权限配置到localStorage
  const permissionData = {
    authorizedUsers: userPermissionForm.value.authorizedUsers,
    permissions: userPermissionForm.value.permissions,
    updateTime: new Date().toISOString()
  }

  localStorage.setItem('fieldTypeUserPermissions', JSON.stringify(permissionData))
  ElMessage.success('用户权限配置保存成功')
  showUserPermissionDialog.value = false
}

// 字段信息数据比对功能
const dataCompareForm = ref({
  dataField: '',
  compareField: '',
  compareOptions: []
})

const compareOptionsList = [
  { label: '数据类型比对', value: 'dataType' },
  { label: '数据长度比对', value: 'dataLength' }
]

const compareResults = ref([])

// 获取字段选项（从缓存数据中获取）
const fieldOptions = computed(() => {
  return tableData.value.map(item => ({
    label: item.name,
    value: item.id
  }))
})

// 获取数据字段A的选项（排除已选择的比对字段B）
const dataFieldOptions = computed(() => {
  return fieldOptions.value.filter(option =>
    option.value !== dataCompareForm.value.compareField
  )
})

// 获取比对字段B的选项（排除已选择的数据字段A）
const compareFieldOptions = computed(() => {
  return fieldOptions.value.filter(option =>
    option.value !== dataCompareForm.value.dataField
  )
})

// 监听字段选择变化，确保不会选择相同的字段
watch(() => dataCompareForm.value.dataField, (newValue) => {
  if (newValue && newValue === dataCompareForm.value.compareField) {
    dataCompareForm.value.compareField = ''
    ElMessage.warning('数据字段A和比对字段B不能选择相同的字段')
  }
})

watch(() => dataCompareForm.value.compareField, (newValue) => {
  if (newValue && newValue === dataCompareForm.value.dataField) {
    dataCompareForm.value.dataField = ''
    ElMessage.warning('数据字段A和比对字段B不能选择相同的字段')
  }
})

const onStartCompare = () => {
  if (!dataCompareForm.value.dataField || !dataCompareForm.value.compareField) {
    ElMessage.warning('请选择要比对的字段')
    return
  }

  if (dataCompareForm.value.dataField === dataCompareForm.value.compareField) {
    ElMessage.warning('数据字段和比对字段不能相同')
    return
  }

  if (dataCompareForm.value.compareOptions.length === 0) {
    ElMessage.warning('请选择比对选项')
    return
  }

  // 获取字段数据
  const dataFieldInfo = tableData.value.find(item => item.id === dataCompareForm.value.dataField)
  const compareFieldInfo = tableData.value.find(item => item.id === dataCompareForm.value.compareField)

  if (!dataFieldInfo || !compareFieldInfo) {
    ElMessage.error('字段信息不存在')
    return
  }

  // 执行比对逻辑
  const results = []
  const compareOptions = dataCompareForm.value.compareOptions

  let differences = []

  // 数据类型比对
  if (compareOptions.includes('dataType') && dataFieldInfo.type !== compareFieldInfo.type) {
    differences.push('数据类型不一致')
  }

  // 数据长度比对
  if (compareOptions.includes('dataLength')) {
    if (dataFieldInfo.minLength !== compareFieldInfo.minLength ||
        dataFieldInfo.maxLength !== compareFieldInfo.maxLength) {
      differences.push('数据长度不一致')
    }
  }

  const result = {
    fieldNameA: dataFieldInfo.name,
    fieldNameB: compareFieldInfo.name,
    hasDifference: differences.length > 0 ? '有差异' : '无差异',
    note: differences.length > 0 ? differences.join('和') : '一致'
  }

  compareResults.value = [result]
  ElMessage.success('比对完成')
}

// 新建字段信息管理
const onFieldInfoManage = () => {
  showFieldInfoDialog.value = true
  fieldInfoList.value = loadFieldInfoFromCache()
  applyFieldInfoFilters()
}

// 常用字段分组管理
const onFieldGroupManage = () => {
  showFieldGroupDialog.value = true
  fieldGroupList.value = loadFieldGroupFromCache()
  applyFieldGroupFilters()
}

// 数据加密
const onDataEncryption = (row: FieldType) => {
  currentRow.value = row
  const existingEncryption = loadDataEncryptionFromCache().find(item => item.id === row.id)
  if (existingEncryption) {
    dataEncryptionForm.value = { ...existingEncryption }
  } else {
    dataEncryptionForm.value = {
      id: row.id,
      encryptType: '',
      encryptKey: '',
      description: ''
    }
  }
  showDataEncryptionDialog.value = true
}

// 字段信息筛选和分页
const applyFieldInfoFilters = () => {
  const filtered = [...fieldInfoList.value]
  fieldInfoPagination.total = filtered.length

  const start = (fieldInfoPagination.page - 1) * fieldInfoPagination.size
  const end = start + fieldInfoPagination.size
  // 这里可以添加实际的筛选逻辑
}

// 字段分组筛选和分页
const applyFieldGroupFilters = () => {
  const filtered = [...fieldGroupList.value]
  fieldGroupPagination.total = filtered.length

  const start = (fieldGroupPagination.page - 1) * fieldGroupPagination.size
  const end = start + fieldGroupPagination.size
  // 这里可以添加实际的筛选逻辑
}

// 字段信息CRUD操作
const onFieldInfoAdd = () => {
  fieldInfoDialogMode.value = 'add'
  fieldInfoForm.value = {
    name: '',
    updateTime: new Date().toLocaleString(),
    operator: '张建波'
  }
  showFieldInfoFormDialog.value = true
}

const onFieldInfoEdit = (row: FieldInfo) => {
  fieldInfoDialogMode.value = 'edit'
  fieldInfoForm.value = { ...row }
  showFieldInfoFormDialog.value = true
}

const onFieldInfoDetail = (row: FieldInfo) => {
  fieldInfoDialogMode.value = 'detail'
  fieldInfoForm.value = { ...row }
  showFieldInfoFormDialog.value = true
}

const onFieldInfoDelete = (row: FieldInfo) => {
  ElMessageBox.confirm('确定要删除这条字段信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = fieldInfoList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      fieldInfoList.value.splice(index, 1)
      saveFieldInfoToCache(fieldInfoList.value)
      applyFieldInfoFilters()
      ElMessage.success('删除成功')
    }
  })
}

// 字段分组CRUD操作
const onFieldGroupAdd = () => {
  fieldGroupDialogMode.value = 'add'
  fieldGroupForm.value = {
    name: '',
    updateTime: new Date().toLocaleString(),
    operator: '张建波'
  }
  showFieldGroupFormDialog.value = true
}

const onFieldGroupEdit = (row: FieldGroup) => {
  fieldGroupDialogMode.value = 'edit'
  fieldGroupForm.value = { ...row }
  showFieldGroupFormDialog.value = true
}

const onFieldGroupDetail = (row: FieldGroup) => {
  fieldGroupDialogMode.value = 'detail'
  fieldGroupForm.value = { ...row }
  showFieldGroupFormDialog.value = true
}

const onFieldGroupDelete = (row: FieldGroup) => {
  ElMessageBox.confirm('确定要删除这个字段分组吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = fieldGroupList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      fieldGroupList.value.splice(index, 1)
      saveFieldGroupToCache(fieldGroupList.value)
      applyFieldGroupFilters()
      ElMessage.success('删除成功')
    }
  })
}

// 字段信息保存
const onFieldInfoConfirm = () => {
  if (!fieldInfoFormRef.value) return

  fieldInfoFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const formData = fieldInfoForm.value

      if (fieldInfoDialogMode.value === 'add') {
        const newFieldInfo: FieldInfo = {
          id: generateId(),
          name: formData.name!,
          updateTime: formData.updateTime!,
          operator: formData.operator!
        }
        fieldInfoList.value.push(newFieldInfo)
      } else if (fieldInfoDialogMode.value === 'edit') {
        const index = fieldInfoList.value.findIndex(item => item.id === formData.id)
        if (index !== -1) {
          fieldInfoList.value[index] = { ...formData } as FieldInfo
        }
      }

      saveFieldInfoToCache(fieldInfoList.value)
      showFieldInfoFormDialog.value = false
      applyFieldInfoFilters()
      ElMessage.success(fieldInfoDialogMode.value === 'add' ? '新增成功' : '编辑成功')
    }
  })
}

// 字段分组保存
const onFieldGroupConfirm = () => {
  if (!fieldGroupFormRef.value) return

  fieldGroupFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const formData = fieldGroupForm.value

      if (fieldGroupDialogMode.value === 'add') {
        const newFieldGroup: FieldGroup = {
          id: generateId(),
          name: formData.name!,
          updateTime: formData.updateTime!,
          operator: formData.operator!
        }
        fieldGroupList.value.push(newFieldGroup)
      } else if (fieldGroupDialogMode.value === 'edit') {
        const index = fieldGroupList.value.findIndex(item => item.id === formData.id)
        if (index !== -1) {
          fieldGroupList.value[index] = { ...formData } as FieldGroup
        }
      }

      saveFieldGroupToCache(fieldGroupList.value)
      showFieldGroupFormDialog.value = false
      applyFieldGroupFilters()
      ElMessage.success(fieldGroupDialogMode.value === 'add' ? '新增成功' : '编辑成功')
    }
  })
}

// 数据加密保存
const onDataEncryptionConfirm = () => {
  if (!dataEncryptionFormRef.value) return

  dataEncryptionFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const encryptionList = loadDataEncryptionFromCache()
      const existingIndex = encryptionList.findIndex(item => item.id === dataEncryptionForm.value.id)

      if (existingIndex !== -1) {
        encryptionList[existingIndex] = { ...dataEncryptionForm.value }
      } else {
        encryptionList.push({ ...dataEncryptionForm.value })
      }

      saveDataEncryptionToCache(encryptionList)
      showDataEncryptionDialog.value = false
      ElMessage.success('数据加密设置保存成功')
    }
  })
}

// 字段信息批量删除
const onFieldInfoBatchDelete = () => {
  if (selectedFieldInfoRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  ElMessageBox.confirm(
    `确认删除选中的 ${selectedFieldInfoRows.value.length} 条记录吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const idsToDelete = selectedFieldInfoRows.value.map(row => row.id)
    fieldInfoList.value = fieldInfoList.value.filter(item => !idsToDelete.includes(item.id))
    saveFieldInfoToCache(fieldInfoList.value)
    selectedFieldInfoRows.value = []
    applyFieldInfoFilters()
    ElMessage.success('批量删除成功')
  })
}

// 字段信息批量导出
const onFieldInfoBatchExport = async () => {
  try {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('字段信息数据')

    // 设置表头
    const headers = [
      { header: '序号', key: 'id', width: 10 },
      { header: '字段信息', key: 'name', width: 30 },
      { header: '更新时间', key: 'updateTime', width: 20 },
      { header: '操作人', key: 'operator', width: 15 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加数据
    const exportData = selectedFieldInfoRows.value.length > 0 ? selectedFieldInfoRows.value : fieldInfoList.value
    const exportType = selectedFieldInfoRows.value.length > 0 ? '选中' : '全部'

    exportData.forEach((row, index) => {
      worksheet.addRow({
        id: index + 1,
        name: row.name,
        updateTime: row.updateTime,
        operator: row.operator
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const fileName = `字段信息数据_${exportType}_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)

    ElMessage.success(`成功导出${exportType} ${exportData.length} 条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 字段信息批量导入
const onFieldInfoBatchImport = () => {
  fieldInfoImportDialogVisible.value = true
  fieldInfoImportFileList.value = []
  fieldInfoSelectedFileName.value = ''
}

// 下载字段信息导入模板
const downloadFieldInfoTemplate = () => {
  try {
    // 创建CSV格式的模板数据
    const csvContent = [
      '字段信息,更新时间,操作人',
      '示例字段信息1,2024-01-01 10:00:00,管理员',
      '示例字段信息2,2024-01-02 11:00:00,用户1',
      '示例字段信息3,2024-01-03 12:00:00,用户2'
    ].join('\n')

    // 创建Blob对象
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })

    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', '字段信息导入模板.csv')
    link.style.visibility = 'hidden'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('CSV模板下载成功，Excel模板功能开发中')
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败，请重试')
  }
}

// 字段信息文件选择处理
const handleFieldInfoFileSelect = (file: any, fileList: any[]) => {
  console.log('文件选择:', file, fileList)

  // 验证文件类型 - 支持CSV和Excel格式
  const isCSV = file.raw?.type === 'text/csv' || file.name.endsWith('.csv')
  const isExcel = file.raw?.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.raw?.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls')

  if (!isCSV && !isExcel) {
    ElMessage.error('只能上传 CSV 或 Excel 文件!')
    fieldInfoImportFileList.value = []
    fieldInfoSelectedFileName.value = ''
    return
  }

  // 验证文件大小
  const isLt10M = (file.raw?.size || file.size) / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    fieldInfoImportFileList.value = []
    fieldInfoSelectedFileName.value = ''
    return
  }

  // 更新文件列表和文件名
  fieldInfoImportFileList.value = fileList
  fieldInfoSelectedFileName.value = file.name
  console.log('文件选择成功:', file.name)
}

// 字段信息文件移除处理
const handleFieldInfoFileRemove = () => {
  fieldInfoSelectedFileName.value = ''
  fieldInfoImportFileList.value = []
  console.log('文件已移除')
}

// 字段信息文件上传前处理（阻止自动上传）
const handleFieldInfoFileChange = (file: any) => {
  console.log('before-upload:', file)
  return false // 阻止自动上传，我们手动处理
}

// 字段信息文件上传
const uploadFieldInfoFile = async () => {
  console.log('开始上传文件，文件列表:', fieldInfoImportFileList.value)

  if (fieldInfoImportFileList.value.length === 0) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  try {
    fieldInfoImportLoading.value = true

    // 获取文件对象
    const fileItem = fieldInfoImportFileList.value[0]
    const file = fileItem.raw || fileItem

    console.log('处理文件:', file)

    if (!file) {
      ElMessage.error('文件获取失败，请重新选择文件')
      return
    }

    let importData = []

    // 判断文件类型并处理
    const isCSV = file.name.endsWith('.csv')
    const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls')

    if (isCSV) {
      // 处理CSV文件
      const text = await file.text()
      console.log('CSV文件内容:', text.substring(0, 200) + '...')

      const lines = text.split('\n').filter(line => line.trim())
      console.log('解析行数:', lines.length)

      if (lines.length <= 1) {
        ElMessage.warning('文件中没有数据')
        return
      }

      // 跳过标题行，解析数据行
      const dataLines = lines.slice(1)
      importData = dataLines.map((line, index) => {
        const columns = line.split(',').map(col => col.trim().replace(/"/g, ''))
        console.log(`第${index + 1}行数据:`, columns)

        return {
          id: generateId(),
          name: columns[0] || '',
          updateTime: columns[1] || new Date().toLocaleString(),
          operator: columns[2] || '当前用户'
        }
      }).filter(item => item.name) // 过滤掉空的字段信息

    } else if (isExcel) {
      // 处理Excel文件 - 暂时提示用户转换为CSV
      ElMessage.warning('Excel文件支持正在开发中，请先将文件转换为CSV格式后上传')
      return
    } else {
      ElMessage.error('不支持的文件格式')
      return
    }

    console.log('解析后的数据:', importData)

    if (importData.length === 0) {
      ElMessage.warning('没有有效的数据可导入')
      return
    }

    // 添加到字段信息列表
    fieldInfoList.value.push(...importData)
    saveFieldInfoToCache(fieldInfoList.value)

    ElMessage.success(`成功导入 ${importData.length} 条字段信息`)
    fieldInfoImportDialogVisible.value = false
    fieldInfoImportFileList.value = []
    fieldInfoSelectedFileName.value = ''

  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请检查文件格式')
  } finally {
    fieldInfoImportLoading.value = false
  }
}

// 初始化字段信息模拟数据
const initFieldInfoMockData = (): FieldInfo[] => {
  return [
    { id: '1', name: '基础字段类型', updateTime: '2025.7.2 10:30:31', operator: '张三' },
    { id: '2', name: '复杂字段', updateTime: '2025.7.2 10:30:31', operator: '周三' },
    { id: '3', name: '特殊字段', updateTime: '2025.7.2 10:30:31', operator: '26' },
    { id: '4', name: '文本输入字段', updateTime: '2025.7.2 10:30:31', operator: '周三' },
    { id: '5', name: '数值计算字段', updateTime: '2025.7.2 10:30:31', operator: '26' },
    { id: '6', name: '日期时间字段', updateTime: '2025.7.3 09:15:22', operator: '李四' },
    { id: '7', name: '图片上传字段', updateTime: '2025.7.3 14:20:45', operator: '王五' },
    { id: '8', name: '下拉选择字段', updateTime: '2025.7.4 11:35:18', operator: '赵六' },
    { id: '9', name: '多选框字段', updateTime: '2025.7.4 16:42:33', operator: '孙七' },
    { id: '10', name: '单选按钮字段', updateTime: '2025.7.5 08:55:12', operator: '周八' }
  ]
}

// 初始化字段分组模拟数据
const initFieldGroupMockData = (): FieldGroup[] => {
  return [
    { id: '1', name: '基础信息分组', updateTime: '2025.7.2 10:30:31', operator: '张三' },
    { id: '2', name: '个人信息分组', updateTime: '2025.7.2 10:30:31', operator: '周三' },
    { id: '3', name: '业务信息分组', updateTime: '2025.7.2 10:30:31', operator: '26' },
    { id: '4', name: '医疗健康分组', updateTime: '2025.7.2 10:30:31', operator: '周三' },
    { id: '5', name: '教育培训分组', updateTime: '2025.7.2 10:30:31', operator: '26' },
    { id: '6', name: '证件证书分组', updateTime: '2025.7.3 09:25:14', operator: '李四' },
    { id: '7', name: '交通运输分组', updateTime: '2025.7.3 14:18:37', operator: '王五' },
    { id: '8', name: '金融贸易分组', updateTime: '2025.7.4 11:42:55', operator: '赵六' },
    { id: '9', name: '安全检测分组', updateTime: '2025.7.4 16:33:28', operator: '孙七' },
    { id: '10', name: '系统管理分组', updateTime: '2025.7.5 08:47:19', operator: '周八' }
  ]
}

// 初始化数据
onMounted(() => {
  tableData.value = loadFromCache()

  // 初始化字段信息数据
  let fieldInfoData = loadFieldInfoFromCache()
  if (fieldInfoData.length === 0) {
    fieldInfoData = initFieldInfoMockData()
    saveFieldInfoToCache(fieldInfoData)
  }
  fieldInfoList.value = fieldInfoData

  // 初始化字段分组数据
  let fieldGroupData = loadFieldGroupFromCache()
  if (fieldGroupData.length === 0) {
    fieldGroupData = initFieldGroupMockData()
    saveFieldGroupToCache(fieldGroupData)
  }
  fieldGroupList.value = fieldGroupData

  // 加载自动填充规则
  loadAutoFillRules()
  // 更新字段类型选项
  updateFieldTypeOptions()

  applyFilters()
})
</script>

<template>
  <div class="field-type">
    <Block title="字段类型管理" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <el-button size="small" type="primary" @click="onFieldInfoManage">新建字段信息</el-button>
        <el-button size="small" type="primary" @click="onFieldGroupManage">常用字段分组</el-button>
        <el-button size="small" type="primary" @click="onClickAdd">新建常用字段</el-button>
        <el-button size="small" type="success" @click="onBatchExport">批量导出</el-button>
        <el-button size="small" type="info" @click="onBatchImport">批量导入</el-button>
        <el-button size="small" type="warning" @click="onUpdateLog">更新日志</el-button>
        <el-button size="small" type="primary" @click="onFieldTemplate">字段信息模板</el-button>
        <el-button
          size="small"
          type="danger"
          :disabled="selectedRowsCount === 0"
          @click="onBatchDelete"
        >
          批量删除
        </el-button>
        <el-dropdown @command="onMoreAction" style="margin-left: 8px;">
          <el-button size="small">
            更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="autoFill">自动填充数据</el-dropdown-item>
              <el-dropdown-item command="fieldUsage">字段信息使用统计</el-dropdown-item>
              <el-dropdown-item command="userPermission">用户权限</el-dropdown-item>
              <el-dropdown-item command="dataCompare">字段信息数据比对</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>

      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="字段名称">
              <el-input v-model="searchForm.name" placeholder="请输入字段名称" clearable />
            </el-form-item>
            <el-form-item label="字段类型">
              <el-select v-model="searchForm.type" placeholder="请选择字段类型" clearable>
                <el-option label="字符型" value="字符型" />
                <el-option label="数值型" value="数值型" />
                <el-option label="image类型" value="image类型" />
                <el-option label="日期型" value="日期型" />
              </el-select>
            </el-form-item>
            <el-form-item label="最小长度">
              <el-input-number v-model="searchForm.minLength" placeholder="请输入最小长度" :min="0" />
            </el-form-item>
            <el-form-item label="最大长度">
              <el-input-number v-model="searchForm.maxLength" placeholder="请输入最大长度" :min="0" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>

      <!-- 列表 -->
      <BaseTableComp
        ref="tableRef"
        :data="getCurrentPageData()"
        :colData="columns"
        :buttons="buttons"
        :loading="loading"
        :checkbox="true"
        :row-key="'id'"
        :reserve-selection="true"
        :auto-height="false"
        :height="tableHeight"
        :currentPage="pagination.page"
        :pageSize="pagination.size"
        :total="pagination.total"
        :visibleSetting="false"
        :visibleIndex="true"
        @clickButton="onTableClickButton"
        @selection-change="onSelectionChange"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      >
        <template #status="{ rowData }">
          <el-switch
            v-model="rowData.status"
            @change="onStatusChange(rowData)"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </template>
      </BaseTableComp>
    </Block>

    <!-- 更新日志弹窗 -->
    <Dialog
      v-model="updateLogDialogVisible"
      title="字段更新日志"
      width="800px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="取消"
      @click-cancel="updateLogDialogVisible = false"
    >
      <div style="margin-bottom: 16px;">
        <el-button type="primary" size="small" @click="onAddUpdateLog">新增</el-button>
      </div>
      <el-table :data="updateLogList" border>
        <el-table-column prop="id" label="序号" width="80" />
        <el-table-column prop="content" label="更新内容" />
        <el-table-column prop="version" label="更新版本" width="120" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column prop="operator" label="操作人" width="120" />
      </el-table>
    </Dialog>

    <!-- 新增更新日志弹窗 -->
    <Dialog
      v-model="addUpdateLogDialogVisible"
      title="字段更新日志"
      width="600px"
      :destroy-on-close="true"
      :loading="updateLogLoading"
      loading-text="保存中"
      @closed="resetUpdateLogForm"
      @click-confirm="onSaveUpdateLog"
    >
      <el-form :model="updateLogForm" :rules="updateLogFormRules" ref="updateLogFormRef" label-width="100px">
        <el-form-item label="更新内容" prop="content" required>
          <el-input v-model="updateLogForm.content" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime" required>
          <el-date-picker
            v-model="updateLogForm.updateTime"
            type="datetime"
            placeholder="请选择更新时间"
            format="YYYY.M.D HH:mm:ss"
            value-format="YYYY.M.D HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="operator" required>
          <el-input v-model="updateLogForm.operator" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="更新版本" prop="version" required>
          <el-input v-model="updateLogForm.version" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 批量导入弹窗 -->
    <Dialog
      v-model="batchImportDialogVisible"
      title="批量导入"
      width="600px"
      :destroy-on-close="true"
      :loading="importLoading"
      loading-text="导入中"
      confirm-text="上传"
      @click-confirm="uploadFile"
    >
      <div class="import-content">
        <div class="import-header">
          <el-icon><Upload /></el-icon>
          <span>导入须知</span>
        </div>

        <div class="import-steps">
          <div class="step-title">操作流程：</div>
          <div class="steps">
            <div class="step">
              <span class="step-number">1</span>
              <span class="step-text">下载模板</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">2</span>
              <span class="step-text">填写表格</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">3</span>
              <span class="step-text">上传表格</span>
            </div>
          </div>
        </div>

        <div class="template-download">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            导入模板下载
          </el-button>
        </div>

        <div class="upload-area">
          <el-upload
            class="upload-demo"
            drag
            :file-list="importFileList"
            :before-upload="handleFileChange"
            :on-remove="handleFileRemove"
            :on-change="handleFileSelect"
            :limit="1"
            accept=".xlsx,.xls"
            :auto-upload="false"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              点击或将文件拖拽到这里上传
            </div>
            <template #tip>
              <div class="el-upload__tip">
                导入模式为业务表中已有数据更新，业务表中不含数据新增，仅支持后缀名为xlsx文件
              </div>
            </template>
          </el-upload>

          <!-- 显示选中的文件名 -->
          <div v-if="selectedFileName" class="selected-file">
            <div class="file-name">{{ selectedFileName }}</div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 新增/编辑弹窗 -->
    <Dialog
      v-model="showDialogForm"
      :title="currentRow ? '编辑字段类型' : '新增常用字段类型'"
      width="500px"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="currentRow = null; Object.assign(dialogForm, {})"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
      />
    </Dialog>

    <!-- 详情弹窗 -->
    <Dialog
      v-model="showDetailDialog"
      title="字段类型详情"
      width="500px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showDetailDialog = false"
    >
      <Form
        v-if="currentRow"
        v-model="currentRow"
        :props="detailFormProps"
        :enable-button="false"
      />
    </Dialog>

    <!-- 自动填充数据弹窗 -->
    <Dialog
      v-model="showAutoFillDialog"
      title="自动填充规则设置"
      width="500px"
      :destroy-on-close="true"
      @click-confirm="onAutoFillConfirm"
      @click-cancel="showAutoFillDialog = false"
    >
      <el-form ref="autoFillFormRef" :model="autoFillForm" label-width="100px">
        <el-form-item label="序号：" prop="sequence" :rules="[{ required: true, message: '请选择自增', trigger: 'change' }]">
          <el-select v-model="autoFillForm.sequence" placeholder="请选择自增" style="width: 100%">
            <el-option label="自增" value="auto" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型：" prop="dataType" :rules="[{ required: true, message: '请选择数据类型', trigger: 'change' }]">
          <el-select v-model="autoFillForm.dataType" multiple placeholder="请选择数据类型（可多选）" style="width: 100%">
            <el-option
              v-for="option in dataTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 用户权限弹窗 -->
    <Dialog
      v-model="showUserPermissionDialog"
      title="字段管理用户权限配置"
      width="600px"
      :destroy-on-close="true"
      @click-confirm="onUserPermissionConfirm"
      @click-cancel="showUserPermissionDialog = false"
    >
      <el-form :model="userPermissionForm" label-width="100px">
        <el-form-item label="授权人员：" prop="authorizedUsers" :rules="[{ required: true, message: '请选择授权人员', trigger: 'change' }]">
          <el-select v-model="userPermissionForm.authorizedUsers" multiple placeholder="请选择" style="width: 100%">
            <el-option
              v-for="user in mockUsers"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="操作权限：">
          <el-checkbox-group v-model="userPermissionForm.permissions">
            <el-checkbox
              v-for="permission in permissionOptions"
              :key="permission.value"
              :label="permission.value"
            >
              {{ permission.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 字段信息数据比对弹窗 -->
    <Dialog
      v-model="showDataCompareDialog"
      title="字段信息数据比对"
      width="800px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="取消"
      @click-cancel="showDataCompareDialog = false"
    >
      <div style="margin-bottom: 20px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据字段A：">
              <el-select v-model="dataCompareForm.dataField" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="option in dataFieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="比对字段B：">
              <el-select v-model="dataCompareForm.compareField" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="option in compareFieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="比对类型：">
          <el-checkbox-group v-model="dataCompareForm.compareOptions">
            <el-checkbox
              v-for="option in compareOptionsList"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-button type="primary" @click="onStartCompare">开始比对</el-button>
      </div>

      <!-- 比对结果表格 -->
      <div v-if="compareResults.length > 0">
        <h4>比对结果：</h4>
        <el-table :data="compareResults" border style="width: 100%">
          <el-table-column prop="fieldNameA" label="字段名称A" width="150" />
          <el-table-column prop="fieldNameB" label="字段名称B" width="150" />
          <el-table-column prop="hasDifference" label="是否有差异" width="120" />
          <el-table-column prop="note" label="备注" />
        </el-table>
      </div>
    </Dialog>

    <!-- 新建字段信息弹窗 -->
    <Dialog
      v-model="showFieldInfoDialog"
      title="字段信息"
      width="1200px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="取消"
      @click-cancel="showFieldInfoDialog = false"
    >
      <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="onFieldInfoAdd">新增</el-button>
        <el-button type="danger" :disabled="selectedFieldInfoRows.length === 0" @click="onFieldInfoBatchDelete">批量删除</el-button>
        <el-button type="success" @click="onFieldInfoBatchExport">批量导出</el-button>
        <el-button type="info" @click="onFieldInfoBatchImport">批量导入</el-button>
      </div>

      <el-table :data="fieldInfoList" border style="width: 100%" @selection-change="(selection) => selectedFieldInfoRows = selection">
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="80">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="字段信息" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="onFieldInfoDetail(row)">查看</el-button>
            <el-button type="primary" size="small" @click="onFieldInfoEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="onFieldInfoDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-model:current-page="fieldInfoPagination.page"
          v-model:page-size="fieldInfoPagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="fieldInfoPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="applyFieldInfoFilters"
          @current-change="applyFieldInfoFilters"
        />
      </div>
    </Dialog>

    <!-- 字段信息批量导入弹窗 -->
    <Dialog
      v-model="fieldInfoImportDialogVisible"
      title="字段信息批量导入"
      width="600px"
      :destroy-on-close="true"
      :loading="fieldInfoImportLoading"
      loading-text="导入中"
      confirm-text="上传"
      @click-confirm="uploadFieldInfoFile"
    >
      <div class="import-content">
        <div class="import-header">
          <el-icon><Upload /></el-icon>
          <span>导入须知</span>
        </div>

        <div class="import-steps">
          <div class="step-title">操作流程：</div>
          <div class="steps">
            <div class="step">
              <span class="step-number">1</span>
              <span class="step-text">下载模板</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">2</span>
              <span class="step-text">填写表格</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">3</span>
              <span class="step-text">上传表格</span>
            </div>
          </div>
        </div>

        <div class="template-download">
          <el-button type="primary" @click="downloadFieldInfoTemplate">
            <el-icon><Download /></el-icon>
            字段信息模板下载
          </el-button>
        </div>

        <div class="upload-area">
          <el-upload
            class="upload-demo"
            drag
            v-model:file-list="fieldInfoImportFileList"
            :before-upload="handleFieldInfoFileChange"
            :on-remove="handleFieldInfoFileRemove"
            :on-change="handleFieldInfoFileSelect"
            :limit="1"
            accept=".csv,.xlsx,.xls"
            :auto-upload="false"
            :show-file-list="true"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              点击或将文件拖拽到这里上传
            </div>
            <template #tip>
              <div class="el-upload__tip">
                导入模式为字段信息数据新增，支持CSV和Excel文件格式（.csv, .xlsx, .xls）
              </div>
            </template>
          </el-upload>

          <!-- 显示选中的文件名 -->
          <div v-if="fieldInfoSelectedFileName" class="selected-file">
            <div class="file-name">已选择文件：{{ fieldInfoSelectedFileName }}</div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 常用字段分组弹窗 -->
    <Dialog
      v-model="showFieldGroupDialog"
      title="常用字段分组"
      width="1000px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="取消"
      @click-cancel="showFieldGroupDialog = false"
    >
      <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="onFieldGroupAdd">新增</el-button>
      </div>

      <el-table :data="fieldGroupList" border style="width: 100%">
        <el-table-column label="序号" width="80">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="常用字段分组名称" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="onFieldGroupDetail(row)">查看</el-button>
            <el-button type="primary" size="small" @click="onFieldGroupEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="onFieldGroupDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-model:current-page="fieldGroupPagination.page"
          v-model:page-size="fieldGroupPagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="fieldGroupPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="applyFieldGroupFilters"
          @current-change="applyFieldGroupFilters"
        />
      </div>
    </Dialog>

    <!-- 数据加密弹窗 -->
    <Dialog
      v-model="showDataEncryptionDialog"
      title="数据加密"
      width="600px"
      :destroy-on-close="true"
      @click-confirm="onDataEncryptionConfirm"
      @click-cancel="showDataEncryptionDialog = false"
    >
      <el-form ref="dataEncryptionFormRef" :model="dataEncryptionForm" :rules="dataEncryptionFormRules" label-width="100px">
        <el-form-item label="加密类型" prop="encryptType">
          <el-select v-model="dataEncryptionForm.encryptType" placeholder="请选择 AES / RSA /MD5" style="width: 100%">
            <el-option label="AES" value="AES" />
            <el-option label="RSA" value="RSA" />
            <el-option label="MD5" value="MD5" />
          </el-select>
        </el-form-item>
        <el-form-item label="加密密钥" prop="encryptKey">
          <el-input v-model="dataEncryptionForm.encryptKey" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dataEncryptionForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 字段信息表单弹窗 -->
    <Dialog
      v-model="showFieldInfoFormDialog"
      :title="fieldInfoDialogMode === 'add' ? '新增字段信息' : fieldInfoDialogMode === 'edit' ? '编辑字段信息' : '字段信息详情'"
      width="600px"
      :destroy-on-close="true"
      :visible-confirm-button="fieldInfoDialogMode !== 'detail'"
      @click-confirm="onFieldInfoConfirm"
      @click-cancel="showFieldInfoFormDialog = false"
    >
      <el-form
        ref="fieldInfoFormRef"
        :model="fieldInfoForm"
        :rules="fieldInfoFormRules"
        label-width="120px"
      >
        <el-form-item label="字段信息" prop="name">
          <el-input
            v-model="fieldInfoForm.name"
            placeholder="请输入"
            :disabled="fieldInfoDialogMode === 'detail'"
          />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime">
          <el-input
            v-model="fieldInfoForm.updateTime"
            placeholder="请输入"
            :disabled="fieldInfoDialogMode === 'detail'"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="operator">
          <el-input
            v-model="fieldInfoForm.operator"
            placeholder="请输入"
            :disabled="fieldInfoDialogMode === 'detail'"
          />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 字段分组表单弹窗 -->
    <Dialog
      v-model="showFieldGroupFormDialog"
      :title="fieldGroupDialogMode === 'add' ? '新增常用字段分组' : fieldGroupDialogMode === 'edit' ? '编辑常用字段分组' : '常用字段分组详情'"
      width="600px"
      :destroy-on-close="true"
      :visible-confirm-button="fieldGroupDialogMode !== 'detail'"
      @click-confirm="onFieldGroupConfirm"
      @click-cancel="showFieldGroupFormDialog = false"
    >
      <el-form
        ref="fieldGroupFormRef"
        :model="fieldGroupForm"
        :rules="fieldGroupFormRules"
        label-width="180px"
      >
        <el-form-item label="常用字段分组名称" prop="name">
          <el-input
            v-model="fieldGroupForm.name"
            placeholder="请输入"
            :disabled="fieldGroupDialogMode === 'detail'"
          />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime">
          <el-input
            v-model="fieldGroupForm.updateTime"
            placeholder="请输入"
            :disabled="fieldGroupDialogMode === 'detail'"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="operator">
          <el-input
            v-model="fieldGroupForm.operator"
            placeholder="请输入"
            :disabled="fieldGroupDialogMode === 'detail'"
          />
        </el-form-item>
      </el-form>
    </Dialog>
  </div>
</template>

<style scoped>
.import-content {
  padding: 20px 0;
}

.import-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

.import-header .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.import-steps {
  margin-bottom: 20px;
}

.step-title {
  margin-bottom: 12px;
  font-weight: bold;
}

.steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  font-weight: bold;
  margin-bottom: 8px;
}

.step-text {
  font-size: 14px;
  color: #666;
}

.step-divider {
  width: 80px;
  height: 2px;
  background-color: #e4e7ed;
  margin: 0 20px;
  margin-top: -20px;
}

.template-download {
  text-align: center;
  margin-bottom: 30px;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
}

.upload-demo {
  width: 100%;
}

.el-upload__tip {
  color: #999;
  font-size: 12px;
  margin-top: 10px;
  line-height: 1.4;
}

.selected-file {
  margin-top: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.file-name {
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>

<route>
{
  meta: {
    title: '字段类型管理',
  },
}
</route>

<style scoped lang="scss">
.field-type {
  .search {
    margin-bottom: 16px;

    .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 16px;
      }
    }
  }

  .detail-content {
    padding: 20px 0;
  }
}
</style>
